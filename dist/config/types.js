/**
 * Configuration types for MCP multi-server agent
 */
/**
 * Environment variable names used by the application
 */
export const ENV_VARS = {
    OPENAI_API_KEY: 'OPENAI_API_KEY',
    OPENAI_BASE_URL: 'OPENAI_BASE_URL',
    OPENAI_ORGANIZATION: 'OPENAI_ORGANIZATION',
    LOG_LEVEL: 'LOG_LEVEL',
    LOG_FORMAT: 'LOG_FORMAT',
    LOG_FILE: 'LOG_FILE',
    MAX_CONCURRENT_SERVERS: 'MAX_CONCURRENT_SERVERS',
    SERVER_STARTUP_TIMEOUT: 'SERVER_STARTUP_TIMEOUT',
    AGENT_TIMEOUT: 'AGENT_TIMEOUT',
    AGENT_MAX_STEPS: 'AGENT_MAX_STEPS',
};
/**
 * Default configuration values
 */
export const DEFAULT_CONFIG = {
    agent: {
        maxSteps: 10,
        timeout: 60000, // 60 seconds
        autoInitialize: true,
        verbose: false,
    },
    serverManager: {
        enabled: true,
        maxConcurrentServers: 3,
        serverStartupTimeout: 30,
        healthMonitoring: true,
        healthCheckInterval: 30000, // 30 seconds
        autoReconnect: true,
    },
    llm: {
        model: 'gpt-4o',
        temperature: 0.1,
        maxTokens: 4096,
        maxRetries: 3,
        retryDelay: 2000,
    },
    logging: {
        level: 'info',
        format: 'text',
    },
};
//# sourceMappingURL=types.js.map