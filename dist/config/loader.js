/**
 * Configuration loader for MCP multi-server agent
 */
import { config } from 'dotenv';
import { DEFAULT_CONFIG, ENV_VARS } from './types.js';
// Load environment variables
config();
/**
 * Configuration validation error
 */
export class ConfigValidationError extends Error {
    constructor(message) {
        super(`Configuration validation error: ${message}`);
        this.name = 'ConfigValidationError';
    }
}
/**
 * Load configuration from environment variables and defaults
 */
export function loadConfig(customConfig) {
    // Get OpenAI API key from environment
    const openaiApiKey = process.env[ENV_VARS.OPENAI_API_KEY];
    if (!openaiApiKey) {
        throw new ConfigValidationError(`${ENV_VARS.OPENAI_API_KEY} environment variable is required`);
    }
    // Build LLM configuration
    const llmConfig = {
        ...DEFAULT_CONFIG.llm,
        apiKey: openaiApiKey,
        ...(process.env[ENV_VARS.OPENAI_BASE_URL] && { baseURL: process.env[ENV_VARS.OPENAI_BASE_URL] }),
        ...(process.env[ENV_VARS.OPENAI_ORGANIZATION] && { organization: process.env[ENV_VARS.OPENAI_ORGANIZATION] }),
        ...customConfig?.llm,
    };
    // Build agent configuration
    const agentConfig = {
        ...DEFAULT_CONFIG.agent,
        timeout: getEnvNumber(ENV_VARS.AGENT_TIMEOUT, DEFAULT_CONFIG.agent.timeout) ?? DEFAULT_CONFIG.agent.timeout,
        maxSteps: getEnvNumber(ENV_VARS.AGENT_MAX_STEPS, DEFAULT_CONFIG.agent.maxSteps) ?? DEFAULT_CONFIG.agent.maxSteps,
        ...customConfig?.agent,
    };
    // Build server manager configuration
    const serverManagerConfig = {
        ...DEFAULT_CONFIG.serverManager,
        maxConcurrentServers: getEnvNumber(ENV_VARS.MAX_CONCURRENT_SERVERS, DEFAULT_CONFIG.serverManager.maxConcurrentServers) ?? DEFAULT_CONFIG.serverManager.maxConcurrentServers,
        serverStartupTimeout: getEnvNumber(ENV_VARS.SERVER_STARTUP_TIMEOUT, DEFAULT_CONFIG.serverManager.serverStartupTimeout) ?? DEFAULT_CONFIG.serverManager.serverStartupTimeout,
        ...customConfig?.serverManager,
    };
    // Build logging configuration
    const loggingConfig = {
        level: process.env[ENV_VARS.LOG_LEVEL] || DEFAULT_CONFIG.logging.level,
        format: process.env[ENV_VARS.LOG_FORMAT] || DEFAULT_CONFIG.logging.format,
        ...customConfig?.logging,
    };
    const logFile = process.env[ENV_VARS.LOG_FILE];
    if (logFile) {
        loggingConfig.file = logFile;
    }
    // Build complete configuration
    const config = {
        servers: customConfig?.servers || getDefaultServers(),
        agent: agentConfig,
        serverManager: serverManagerConfig,
        llm: llmConfig,
        logging: loggingConfig,
        ...(customConfig?.env && { env: customConfig.env }),
    };
    // Validate configuration
    validateConfig(config);
    return config;
}
/**
 * Get a number from environment variable with fallback
 */
function getEnvNumber(envVar, fallback) {
    const value = process.env[envVar];
    if (value === undefined) {
        return fallback;
    }
    const parsed = parseInt(value, 10);
    if (isNaN(parsed)) {
        throw new ConfigValidationError(`Environment variable ${envVar} must be a valid number, got: ${value}`);
    }
    return parsed;
}
/**
 * Get default server configurations
 * These are example servers - users should customize based on their needs
 */
function getDefaultServers() {
    return [
        {
            id: 'filesystem',
            name: 'File System Server',
            description: 'Provides file system operations',
            connectionType: 'stdio',
            command: 'npx',
            args: ['@modelcontextprotocol/server-filesystem', '/tmp'],
            enabled: false, // Disabled by default - user should enable and configure
            priority: 5,
            tags: ['filesystem', 'files'],
            timeout: 30000,
            retry: {
                maxAttempts: 3,
                delayMs: 1000,
                backoffMultiplier: 2,
            },
        },
        {
            id: 'web-browser',
            name: 'Web Browser Server',
            description: 'Provides web browsing capabilities',
            connectionType: 'stdio',
            command: 'npx',
            args: ['@modelcontextprotocol/server-puppeteer'],
            enabled: false, // Disabled by default
            priority: 3,
            tags: ['web', 'browser', 'scraping'],
            timeout: 45000,
            retry: {
                maxAttempts: 2,
                delayMs: 2000,
            },
        },
        {
            id: 'sqlite',
            name: 'SQLite Database Server',
            description: 'Provides SQLite database operations',
            connectionType: 'stdio',
            command: 'npx',
            args: ['@modelcontextprotocol/server-sqlite', 'database.db'],
            enabled: false, // Disabled by default
            priority: 4,
            tags: ['database', 'sqlite', 'sql'],
            timeout: 20000,
            retry: {
                maxAttempts: 3,
                delayMs: 1000,
            },
        },
    ];
}
/**
 * Validate the complete configuration
 */
function validateConfig(config) {
    // Validate LLM configuration
    if (!config.llm.apiKey) {
        throw new ConfigValidationError('LLM API key is required');
    }
    // Validate servers
    if (!Array.isArray(config.servers)) {
        throw new ConfigValidationError('Servers must be an array');
    }
    const enabledServers = config.servers.filter(server => server.enabled);
    if (enabledServers.length === 0) {
        console.warn('Warning: No servers are enabled. The agent will have limited capabilities.');
    }
    // Validate each server configuration
    config.servers.forEach((server, index) => {
        validateServerConfig(server, index);
    });
    // Validate server manager configuration
    if (config.serverManager.enabled && config.serverManager.maxConcurrentServers <= 0) {
        throw new ConfigValidationError('maxConcurrentServers must be greater than 0');
    }
    // Validate agent configuration
    if (config.agent.maxSteps <= 0) {
        throw new ConfigValidationError('Agent maxSteps must be greater than 0');
    }
    if (config.agent.timeout <= 0) {
        throw new ConfigValidationError('Agent timeout must be greater than 0');
    }
}
/**
 * Validate a single server configuration
 */
function validateServerConfig(server, index) {
    const prefix = `Server ${index} (${server.id || 'unnamed'})`;
    if (!server.id) {
        throw new ConfigValidationError(`${prefix}: id is required`);
    }
    if (!server.name) {
        throw new ConfigValidationError(`${prefix}: name is required`);
    }
    if (!server.connectionType) {
        throw new ConfigValidationError(`${prefix}: connectionType is required`);
    }
    // Validate connection-specific requirements
    switch (server.connectionType) {
        case 'http':
        case 'websocket':
        case 'sse':
            if (!server.url) {
                throw new ConfigValidationError(`${prefix}: url is required for ${server.connectionType} connections`);
            }
            break;
        case 'stdio':
            if (!server.command) {
                throw new ConfigValidationError(`${prefix}: command is required for stdio connections`);
            }
            break;
        default:
            throw new ConfigValidationError(`${prefix}: unsupported connectionType: ${server.connectionType}`);
    }
    // Validate timeout
    if (server.timeout !== undefined && server.timeout <= 0) {
        throw new ConfigValidationError(`${prefix}: timeout must be greater than 0`);
    }
    // Validate retry configuration
    if (server.retry) {
        if (server.retry.maxAttempts <= 0) {
            throw new ConfigValidationError(`${prefix}: retry.maxAttempts must be greater than 0`);
        }
        if (server.retry.delayMs < 0) {
            throw new ConfigValidationError(`${prefix}: retry.delayMs must be non-negative`);
        }
    }
}
/**
 * Create a configuration with custom servers
 */
export function createConfig(servers, options) {
    const customConfig = { servers };
    if (options?.llm) {
        customConfig.llm = options.llm;
    }
    if (options?.agent) {
        customConfig.agent = options.agent;
    }
    if (options?.serverManager) {
        customConfig.serverManager = options.serverManager;
    }
    return loadConfig(customConfig);
}
//# sourceMappingURL=loader.js.map